// User types
export interface User {
  id: number;
  username: string;
  email: string;
  createdAt: string;
  reputation: number;
}

export interface AuthUser extends User {
  token: string;
}

// Question types
export interface Question {
  id: number;
  title: string;
  body: string;
  createdAt: string;
  updatedAt?: string;
  userId: number;
  user: User;
  tags: Tag[];
  answers: Answer[];
  votes?: Vote[];
  voteCount: number;
  userVote?: "up" | "down" | null;
}

export interface QuestionSummary {
  id: number;
  title: string;
  body: string;
  createdAt: string;
  updatedAt?: string;
  user: User;
  tags: Tag[];
  answerCount: number;
  voteCount: number;
  userVote?: "up" | "down" | null;
}

export interface CreateQuestionRequest {
  title: string;
  body: string;
  tags: string[];
}

export interface UpdateQuestionRequest {
  title?: string;
  body?: string;
  tags?: string[];
}

// Answer types
export interface Answer {
  id: number;
  body: string;
  createdAt: string;
  updatedAt?: string;
  userId: number;
  user: User;
  questionId: number;
  votes?: Vote[];
  voteCount: number;
  userVote?: "up" | "down" | null;
  isAIGenerated?: boolean;
}

export interface CreateAnswerRequest {
  body: string;
  questionId: number;
  isAIGenerated?: boolean;
}

export interface UpdateAnswerRequest {
  body: string;
}

// Vote types
export interface Vote {
  id: number;
  userId: number;
  questionId?: number;
  answerId?: number;
  isUpvote: boolean;
  createdAt: string;
}

export interface VoteRequest {
  questionId?: number;
  answerId?: number;
  isUpvote: boolean;
}

// Tag types
export interface Tag {
  id: number;
  name: string;
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search and filter types
export interface QuestionFilters {
  search?: string;
  tags?: string[];
  sortBy?: "newest" | "votes" | "activity";
  page?: number;
  limit?: number;
}

// Form types
export interface FormErrors {
  [key: string]: string;
}

// Component prop types
export interface PageProps {
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

// Cache types
export interface CachedQuestionData {
  data: CreateQuestionRequest;
  timestamp: number;
  expiresAt: number;
}

// Similarity check types
export interface SimilarQuestion {
  id: number;
  title: string;
  body: string;
  createdAt: string;
  user: User;
  tags: Tag[];
  answerCount: number;
  voteCount: number;
  similarityScore: number;
}

export interface SuggestedAnswer {
  sourceQuestionId?: number;
  sourceQuestionTitle: string;
  answerBody: string;
  answerSource: "existing" | "ai_generated";
  relevanceScore: number;
  originalAnswerId?: number;
  originalAuthor?: User;
  originalCreatedAt?: string;
  originalVoteCount?: number;
  isAcceptedAnswer: boolean;
}

export interface SimilarQuestionsResponse {
  hasSimilarQuestions: boolean;
  similarQuestions: SimilarQuestion[];
  suggestionMessage: string;
  hasSuggestedAnswers: boolean;
  suggestedAnswers: SuggestedAnswer[];
  canAutoAnswer: boolean;
  autoAnswerMessage: string;
}

// User profile types
export interface UserProfile {
  id: number;
  username: string;
  email: string;
  createdAt: string;
  reputation: number;
  questionCount: number;
  answerCount: number;
}
