using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using AutoMapper;
using WebApplication1.Data;
using WebApplication1.DTOs;
using WebApplication1.Models;

namespace WebApplication1.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;

        public UsersController(ApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<UserProfileDto>> GetUserProfile(int id)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.Questions)
                        .ThenInclude(q => q.Votes)
                    .Include(u => u.Answers)
                        .ThenInclude(a => a.Votes)
                    .FirstOrDefaultAsync(u => u.Id == id);

                if (user == null)
                {
                    return NotFound(new { message = "User not found" });
                }

                var userProfile = new UserProfileDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    CreatedAt = user.CreatedAt,
                    Reputation = CalculateReputation(user),
                    QuestionCount = user.Questions.Count,
                    AnswerCount = user.Answers.Count
                };

                return Ok(userProfile);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while fetching user profile", details = ex.Message });
            }
        }

        [HttpGet("profile")]
        [Authorize]
        public async Task<ActionResult<UserProfileDto>> GetMyProfile()
        {
            var currentUserId = GetCurrentUserId();
            if (!currentUserId.HasValue)
            {
                return Unauthorized();
            }

            Console.WriteLine($"Getting profile for user ID: {currentUserId.Value}");
            return await GetUserProfile(currentUserId.Value);
        }

        private int CalculateReputation(User user)
        {
            // Base reputation
            int reputation = 1;

            // Add points for question votes (5 points per upvote, -2 per downvote)
            foreach (var question in user.Questions)
            {
                var upvotes = question.Votes.Count(v => v.IsUpvote);
                var downvotes = question.Votes.Count(v => !v.IsUpvote);
                reputation += (upvotes * 5) - (downvotes * 2);
            }

            // Add points for answer votes (10 points per upvote, -2 per downvote)
            foreach (var answer in user.Answers)
            {
                var upvotes = answer.Votes.Count(v => v.IsUpvote);
                var downvotes = answer.Votes.Count(v => !v.IsUpvote);
                reputation += (upvotes * 10) - (downvotes * 2);
            }

            // Ensure reputation doesn't go below 1
            return Math.Max(1, reputation);
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }
}
