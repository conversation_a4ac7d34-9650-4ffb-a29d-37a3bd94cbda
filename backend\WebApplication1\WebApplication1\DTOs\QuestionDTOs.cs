using System.ComponentModel.DataAnnotations;

namespace WebApplication1.DTOs
{
    public class CreateQuestionRequestDto
    {
        [Required]
        [StringLength(200, MinimumLength = 10)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MinLength(30)]
        public string Body { get; set; } = string.Empty;

        [Required]
        [MinLength(1)]
        [MaxLength(5)]
        public List<string> Tags { get; set; } = new List<string>();
    }

    public class UpdateQuestionRequestDto
    {
        [StringLength(200, MinimumLength = 10)]
        public string? Title { get; set; }

        [MinLength(30)]
        public string? Body { get; set; }

        [MaxLength(5)]
        public List<string>? Tags { get; set; }
    }

    public class QuestionDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int UserId { get; set; }
        public UserDto User { get; set; } = null!;
        public List<TagDto> Tags { get; set; } = new List<TagDto>();
        public List<AnswerDto> Answers { get; set; } = new List<AnswerDto>();
        public int VoteCount { get; set; }
        public string? UserVote { get; set; } // "up", "down", or null
    }

    public class QuestionSummaryDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public UserDto User { get; set; } = null!;
        public List<TagDto> Tags { get; set; } = new List<TagDto>();
        public int AnswerCount { get; set; }
        public int VoteCount { get; set; }
        public string? UserVote { get; set; } // "up", "down", or null
    }

    public class QuestionFiltersDto
    {
        public string? Search { get; set; }
        public List<string>? Tags { get; set; }
        public string SortBy { get; set; } = "newest"; // "newest", "votes", "activity"
        public int Page { get; set; } = 1;
        public int Limit { get; set; } = 10;
    }

    public class SimilarQuestionCheckDto
    {
        [Required]
        [StringLength(200, MinimumLength = 10)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MinLength(30)]
        public string Body { get; set; } = string.Empty;

        [Required]
        [MinLength(1)]
        [MaxLength(5)]
        public List<string> Tags { get; set; } = new List<string>();
    }

    public class SimilarQuestionsResponseDto
    {
        public bool HasSimilarQuestions { get; set; }
        public List<SimilarQuestionDto> SimilarQuestions { get; set; } = new List<SimilarQuestionDto>();
        public string SuggestionMessage { get; set; } = string.Empty;
        public bool HasSuggestedAnswers { get; set; }
        public List<SuggestedAnswerDto> SuggestedAnswers { get; set; } = new List<SuggestedAnswerDto>();
        public bool CanAutoAnswer { get; set; }
        public string AutoAnswerMessage { get; set; } = string.Empty;
    }

    public class SimilarQuestionDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public UserDto User { get; set; } = null!;
        public List<TagDto> Tags { get; set; } = new List<TagDto>();
        public int AnswerCount { get; set; }
        public int VoteCount { get; set; }
        public double SimilarityScore { get; set; }
    }

    public class SuggestedAnswerDto
    {
        public int? SourceQuestionId { get; set; }
        public string SourceQuestionTitle { get; set; } = string.Empty;
        public string AnswerBody { get; set; } = string.Empty;
        public string AnswerSource { get; set; } = string.Empty; // "existing" or "ai_generated"
        public double RelevanceScore { get; set; }
        public int? OriginalAnswerId { get; set; }
        public UserDto? OriginalAuthor { get; set; }
        public DateTime? OriginalCreatedAt { get; set; }
        public int? OriginalVoteCount { get; set; }
        public bool IsAcceptedAnswer { get; set; }
    }

    public class PaginatedResponseDto<T>
    {
        public List<T> Data { get; set; } = new List<T>();
        public int Total { get; set; }
        public int Page { get; set; }
        public int Limit { get; set; }
        public int TotalPages { get; set; }
    }
}
