!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(n="undefined"!=typeof globalThis?globalThis:n||self).StyleToObject=e()}(this,(function(){"use strict";function n(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,r=/\n/g,t=/^\s*/,o=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,u=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,c=/^[;\s]*/,f=/^\s+|\s+$/g,a="";function s(n){return n?n.replace(f,a):a}var l=n((function(n,f){if("string"!=typeof n)throw new TypeError("First argument must be a string");if(!n)return[];f=f||{};var l=1,p=1;function h(n){var e=n.match(r);e&&(l+=e.length);var t=n.lastIndexOf("\n");p=~t?n.length-t:p+n.length}function v(){var n={line:l,column:p};return function(e){return e.position=new d(n),g(),e}}function d(n){this.start=n,this.end={line:l,column:p},this.source=f.source}function m(e){var r=new Error(f.source+":"+l+":"+p+": "+e);if(r.reason=e,r.filename=f.source,r.line=l,r.column=p,r.source=n,!f.silent)throw r}function y(e){var r=e.exec(n);if(r){var t=r[0];return h(t),n=n.slice(t.length),r}}function g(){y(t)}function w(n){var e;for(n=n||[];e=b();)!1!==e&&n.push(e);return n}function b(){var e=v();if("/"==n.charAt(0)&&"*"==n.charAt(1)){for(var r=2;a!=n.charAt(r)&&("*"!=n.charAt(r)||"/"!=n.charAt(r+1));)++r;if(r+=2,a===n.charAt(r-1))return m("End of comment missing");var t=n.slice(2,r-2);return p+=2,h(t),n=n.slice(r),p+=2,e({type:"comment",comment:t})}}function A(){var n=v(),r=y(o);if(r){if(b(),!y(i))return m("property missing ':'");var t=y(u),f=n({type:"declaration",property:s(r[0].replace(e,a)),value:t?s(t[0].replace(e,a)):a});return y(c),f}}return d.prototype.content=n,g(),function(){var n,e=[];for(w(e);n=A();)!1!==n&&(e.push(n),w(e));return e}()}));return function(n,e){var r=null;if(!n||"string"!=typeof n)return r;var t=l(n),o="function"==typeof e;return t.forEach((function(n){if("declaration"===n.type){var t=n.property,i=n.value;o?e(t,i,n):i&&((r=r||{})[t]=i)}})),r}}));
//# sourceMappingURL=style-to-object.min.js.map
