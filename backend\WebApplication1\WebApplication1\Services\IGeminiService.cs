using WebApplication1.DTOs;
using WebApplication1.Models;

namespace WebApplication1.Services
{
    public interface IGeminiService
    {
        Task<SimilarQuestionsResponseDto> FindSimilarQuestionsAsync(string title, string body, List<string> tags);
        Task<bool> IsQuestionSimilarAsync(string newTitle, string newBody, string existingTitle, string existingBody);
        Task<List<SuggestedAnswerDto>> ExtractBestAnswersAsync(string questionTitle, string questionBody, List<Question> similarQuestions);
        Task<SuggestedAnswerDto?> GenerateAIAnswerAsync(string questionTitle, string questionBody, List<string> tags);
        Task<double> ScoreAnswerRelevanceAsync(string questionTitle, string questionBody, string answerBody);
    }
}
