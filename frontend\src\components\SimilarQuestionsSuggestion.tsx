import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link } from "react-router-dom";
import { SimilarQuestion, SuggestedAnswer } from "../types";

interface SimilarQuestionsSuggestionProps {
  isVisible: boolean;
  isLoading: boolean;
  similarQuestions: SimilarQuestion[];
  suggestionMessage: string;
  suggestedAnswers: SuggestedAnswer[];
  canAutoAnswer: boolean;
  autoAnswerMessage: string;
  onDismiss: () => void;
  onProceedAnyway: () => void;
  onUseAnswer?: (answer: SuggestedAnswer) => void;
}

const SimilarQuestionsSuggestion: React.FC<SimilarQuestionsSuggestionProps> = ({
  isVisible,
  isLoading,
  similarQuestions = [],
  suggestionMessage = "",
  suggestedAnswers = [],
  canAutoAnswer = false,
  autoAnswerMessage = "",
  onDismiss,
  onProceedAnyway,
  onUseAnswer,
}) => {
  const [activeTab, setActiveTab] = useState<"questions" | "answers">(
    suggestedAnswers?.length > 0 && canAutoAnswer ? "answers" : "questions"
  );
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
            className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
          >
            {/* Header */}
            <div className="bg-orange-500 text-white p-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">
                  {canAutoAnswer ? "Answers Found!" : "Similar Questions Found"}
                </h2>
                <button
                  onClick={onDismiss}
                  className="text-white hover:text-gray-200 text-2xl font-bold"
                >
                  ×
                </button>
              </div>
              <p className="mt-2 text-orange-100">
                {canAutoAnswer ? autoAnswerMessage : suggestionMessage}
              </p>

              {/* Tabs */}
              {(similarQuestions?.length > 0 ||
                suggestedAnswers?.length > 0) && (
                <div className="mt-4 flex space-x-4">
                  {similarQuestions?.length > 0 && (
                    <button
                      onClick={() => setActiveTab("questions")}
                      className={`px-4 py-2 rounded-lg transition-colors ${
                        activeTab === "questions"
                          ? "bg-white text-orange-500 font-semibold"
                          : "bg-orange-400 text-white hover:bg-orange-300"
                      }`}
                    >
                      Similar Questions ({similarQuestions.length})
                    </button>
                  )}
                  {suggestedAnswers?.length > 0 && (
                    <button
                      onClick={() => setActiveTab("answers")}
                      className={`px-4 py-2 rounded-lg transition-colors ${
                        activeTab === "answers"
                          ? "bg-white text-orange-500 font-semibold"
                          : "bg-orange-400 text-white hover:bg-orange-300"
                      }`}
                    >
                      Suggested Answers ({suggestedAnswers.length})
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                  <span className="ml-3 text-gray-600">
                    Checking for similar questions and answers...
                  </span>
                </div>
              ) : activeTab === "questions" && similarQuestions?.length > 0 ? (
                <div className="space-y-4">
                  {similarQuestions.map((question) => (
                    <motion.div
                      key={question.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 }}
                      className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Link
                            to={`/questions/${question.id}`}
                            className="text-lg font-semibold text-blue-600 hover:text-blue-800 block mb-2"
                          >
                            {question.title}
                          </Link>
                          <p className="text-gray-600 text-sm mb-3">
                            {truncateText(question.body, 150)}
                          </p>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-2 mb-3">
                            {question.tags.map((tag) => (
                              <span
                                key={tag.id}
                                className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                              >
                                {tag.name}
                              </span>
                            ))}
                          </div>

                          {/* Meta info */}
                          <div className="flex items-center text-sm text-gray-500 space-x-4">
                            <span>Asked by {question.user.username}</span>
                            <span>{formatDate(question.createdAt)}</span>
                            <span>{question.voteCount} votes</span>
                            <span>{question.answerCount} answers</span>
                          </div>
                        </div>

                        {/* Similarity score */}
                        <div className="ml-4 text-center">
                          <div className="text-sm text-gray-500 mb-1">
                            Similarity
                          </div>
                          <div className="text-lg font-bold text-orange-600">
                            {Math.round(question.similarityScore * 100)}%
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : activeTab === "answers" && suggestedAnswers?.length > 0 ? (
                <div className="space-y-4">
                  {suggestedAnswers.map((answer, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index }}
                      className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <span
                            className={`px-2 py-1 text-xs rounded ${
                              answer.answerSource === "ai_generated"
                                ? "bg-purple-100 text-purple-700"
                                : "bg-blue-100 text-blue-700"
                            }`}
                          >
                            {answer.answerSource === "ai_generated"
                              ? "AI Generated"
                              : "From Community"}
                          </span>
                          <div className="text-sm text-gray-500">
                            Relevance: {Math.round(answer.relevanceScore * 100)}
                            %
                          </div>
                        </div>
                        {onUseAnswer && (
                          <button
                            onClick={() => onUseAnswer(answer)}
                            className="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 transition-colors"
                          >
                            Use This Answer
                          </button>
                        )}
                      </div>

                      {answer.sourceQuestionId && (
                        <div className="mb-3">
                          <Link
                            to={`/questions/${answer.sourceQuestionId}`}
                            className="text-sm text-blue-600 hover:text-blue-800"
                          >
                            From: {answer.sourceQuestionTitle}
                          </Link>
                        </div>
                      )}

                      <div className="prose prose-sm max-w-none mb-3">
                        <div className="text-gray-700 whitespace-pre-wrap">
                          {answer.answerBody}
                        </div>
                      </div>

                      {answer.originalAuthor && (
                        <div className="flex items-center text-sm text-gray-500 space-x-4">
                          <span>
                            Originally by {answer.originalAuthor.username}
                          </span>
                          {answer.originalCreatedAt && (
                            <span>{formatDate(answer.originalCreatedAt)}</span>
                          )}
                          {answer.originalVoteCount !== undefined && (
                            <span>{answer.originalVoteCount} votes</span>
                          )}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-500">
                    {activeTab === "questions"
                      ? "No similar questions found."
                      : "No suggested answers available."}
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-6 py-4 flex justify-between items-center">
              <div className="text-sm text-gray-600">
                {canAutoAnswer
                  ? "High-quality answers are available! Consider using one instead of posting a new question."
                  : suggestedAnswers?.length > 0
                  ? "Review the suggested answers before posting your question."
                  : similarQuestions?.length > 0
                  ? "Please check if any of these questions answer your problem before posting."
                  : "Your question appears to be unique!"}
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={onDismiss}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Review & Edit
                </button>
                <button
                  onClick={onProceedAnyway}
                  className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
                >
                  Post Anyway
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SimilarQuestionsSuggestion;
