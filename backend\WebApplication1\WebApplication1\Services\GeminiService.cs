using System.Text;
using System.Text.Json;
using WebApplication1.DTOs;
using WebApplication1.Data;
using WebApplication1.Models;
using Microsoft.EntityFrameworkCore;
using AutoMapper;

namespace WebApplication1.Services
{
    public class GeminiService : IGeminiService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly string _apiKey;
        private readonly string _baseUrl;
        private readonly string _model;

        public GeminiService(HttpClient httpClient, IConfiguration configuration, ApplicationDbContext context, IMapper mapper)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _context = context;
            _mapper = mapper;
            _apiKey = _configuration["GeminiSettings:ApiKey"] ?? throw new ArgumentNullException("Gemini API key not configured");
            _baseUrl = _configuration["GeminiSettings:BaseUrl"] ?? "https://generativelanguage.googleapis.com/v1beta";
            _model = _configuration["GeminiSettings:Model"] ?? "gemini-1.5-flash";
        }

        public async Task<SimilarQuestionsResponseDto> FindSimilarQuestionsAsync(string title, string body, List<string> tags)
        {
            try
            {
                // Get existing questions from database with answers
                var existingQuestions = await _context.Questions
                    .Include(q => q.User)
                    .Include(q => q.QuestionTags)
                        .ThenInclude(qt => qt.Tag)
                    .Include(q => q.Answers)
                        .ThenInclude(a => a.User)
                    .Include(q => q.Answers)
                        .ThenInclude(a => a.Votes)
                    .Include(q => q.Votes)
                    .OrderByDescending(q => q.CreatedAt)
                    .Take(50) // Limit to recent questions for performance
                    .ToListAsync();

                if (!existingQuestions.Any())
                {
                    // Try to generate AI answer if no similar questions exist
                    var aiAnswer = await GenerateAIAnswerAsync(title, body, tags);
                    return new SimilarQuestionsResponseDto
                    {
                        HasSimilarQuestions = false,
                        SuggestionMessage = "No existing questions found to compare against.",
                        HasSuggestedAnswers = aiAnswer != null,
                        SuggestedAnswers = aiAnswer != null ? new List<SuggestedAnswerDto> { aiAnswer } : new List<SuggestedAnswerDto>(),
                        CanAutoAnswer = aiAnswer != null,
                        AutoAnswerMessage = aiAnswer != null ? "AI has generated a potential answer for your question." : ""
                    };
                }

                var similarQuestions = new List<SimilarQuestionDto>();
                var questionsWithAnswers = new List<Question>();

                // Use Gemini to analyze similarity for each question
                foreach (var question in existingQuestions)
                {
                    var isSimlar = await IsQuestionSimilarAsync(title, body, question.Title, question.Body);

                    if (isSimlar)
                    {
                        var similarQuestion = new SimilarQuestionDto
                        {
                            Id = question.Id,
                            Title = question.Title,
                            Body = question.Body.Length > 200 ? question.Body.Substring(0, 200) + "..." : question.Body,
                            CreatedAt = question.CreatedAt,
                            User = _mapper.Map<UserDto>(question.User),
                            Tags = question.QuestionTags.Select(qt => _mapper.Map<TagDto>(qt.Tag)).ToList(),
                            AnswerCount = question.Answers.Count,
                            VoteCount = question.Votes.Sum(v => v.IsUpvote ? 1 : -1),
                            SimilarityScore = 0.8 // Placeholder - could be enhanced with actual scoring
                        };
                        similarQuestions.Add(similarQuestion);
                        questionsWithAnswers.Add(question);
                    }

                    // Limit to top 5 similar questions to avoid overwhelming the user
                    if (similarQuestions.Count >= 5)
                        break;
                }

                // Extract best answers from similar questions
                var suggestedAnswers = new List<SuggestedAnswerDto>();
                if (questionsWithAnswers.Any())
                {
                    suggestedAnswers = await ExtractBestAnswersAsync(title, body, questionsWithAnswers);
                }

                // If no good answers found in similar questions, try AI generation
                if (!suggestedAnswers.Any())
                {
                    var aiAnswer = await GenerateAIAnswerAsync(title, body, tags);
                    if (aiAnswer != null)
                    {
                        suggestedAnswers.Add(aiAnswer);
                    }
                }

                return new SimilarQuestionsResponseDto
                {
                    HasSimilarQuestions = similarQuestions.Any(),
                    SimilarQuestions = similarQuestions.OrderByDescending(q => q.SimilarityScore).ToList(),
                    SuggestionMessage = similarQuestions.Any()
                        ? "We found some similar questions. Please check if any of these answer your question before posting."
                        : "Your question appears to be unique. Feel free to post it!",
                    HasSuggestedAnswers = suggestedAnswers.Any(),
                    SuggestedAnswers = suggestedAnswers.OrderByDescending(a => a.RelevanceScore).ToList(),
                    CanAutoAnswer = suggestedAnswers.Any(a => a.RelevanceScore > 0.7),
                    AutoAnswerMessage = suggestedAnswers.Any(a => a.RelevanceScore > 0.7)
                        ? "We found high-quality answers that might solve your question. You can use one of these answers directly!"
                        : suggestedAnswers.Any()
                            ? "We found some potential answers. Please review them before posting your question."
                            : ""
                };
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the question creation process
                Console.WriteLine($"Error in similarity check: {ex.Message}");
                return new SimilarQuestionsResponseDto
                {
                    HasSimilarQuestions = false,
                    SuggestionMessage = "Unable to check for similar questions at this time. You can still post your question."
                };
            }
        }

        public async Task<bool> IsQuestionSimilarAsync(string newTitle, string newBody, string existingTitle, string existingBody)
        {
            try
            {
                var prompt = $@"
Compare these two programming questions and determine if they are asking about the same or very similar problems.

Question 1:
Title: {newTitle}
Body: {newBody}

Question 2:
Title: {existingTitle}
Body: {existingBody}

Respond with only 'true' if the questions are similar (asking about the same core problem, concept, or issue), or 'false' if they are different.
Consider questions similar if they:
- Ask about the same programming concept or technology
- Have the same core problem even with different wording
- Would likely have overlapping answers

Respond with only 'true' or 'false', nothing else.";

                var requestBody = new
                {
                    contents = new[]
                    {
                        new
                        {
                            parts = new[]
                            {
                                new { text = prompt }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_baseUrl}/models/{_model}:generateContent?key={_apiKey}";
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var geminiResponse = JsonSerializer.Deserialize<GeminiResponse>(responseContent);

                    var text = geminiResponse?.candidates?.FirstOrDefault()?.content?.parts?.FirstOrDefault()?.text?.Trim().ToLower();
                    return text == "true";
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling Gemini API: {ex.Message}");
                return false;
            }
        }

        public async Task<List<SuggestedAnswerDto>> ExtractBestAnswersAsync(string questionTitle, string questionBody, List<Question> similarQuestions)
        {
            var suggestedAnswers = new List<SuggestedAnswerDto>();

            try
            {
                foreach (var question in similarQuestions.Take(3)) // Limit to top 3 similar questions
                {
                    // Get the best answers from this question (highest voted, accepted answers first)
                    var bestAnswers = question.Answers
                        .Where(a => !string.IsNullOrWhiteSpace(a.Body) && a.Body.Length > 50) // Filter out short/empty answers
                        .OrderByDescending(a => a.Votes.Sum(v => v.IsUpvote ? 1 : -1)) // Order by vote count
                        .Take(2) // Take top 2 answers per question
                        .ToList();

                    foreach (var answer in bestAnswers)
                    {
                        // Score the relevance of this answer to the new question
                        var relevanceScore = await ScoreAnswerRelevanceAsync(questionTitle, questionBody, answer.Body);

                        if (relevanceScore > 0.5) // Only include answers with decent relevance
                        {
                            var suggestedAnswer = new SuggestedAnswerDto
                            {
                                SourceQuestionId = question.Id,
                                SourceQuestionTitle = question.Title,
                                AnswerBody = answer.Body,
                                AnswerSource = "existing",
                                RelevanceScore = relevanceScore,
                                OriginalAnswerId = answer.Id,
                                OriginalAuthor = _mapper.Map<UserDto>(answer.User),
                                OriginalCreatedAt = answer.CreatedAt,
                                OriginalVoteCount = answer.Votes.Sum(v => v.IsUpvote ? 1 : -1),
                                IsAcceptedAnswer = false // You might want to add this field to your Answer model
                            };
                            suggestedAnswers.Add(suggestedAnswer);
                        }
                    }
                }

                return suggestedAnswers.OrderByDescending(a => a.RelevanceScore).Take(3).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting best answers: {ex.Message}");
                return new List<SuggestedAnswerDto>();
            }
        }

        public async Task<SuggestedAnswerDto?> GenerateAIAnswerAsync(string questionTitle, string questionBody, List<string> tags)
        {
            try
            {
                var prompt = $@"
You are a helpful programming assistant. A user has asked the following question:

Title: {questionTitle}
Question: {questionBody}
Tags: {string.Join(", ", tags)}

Please provide a comprehensive, helpful answer to this programming question. Your answer should:
1. Be technically accurate and up-to-date
2. Include code examples where appropriate
3. Explain the concepts clearly
4. Be practical and actionable
5. Be at least 100 words but not more than 500 words

If you cannot provide a good answer or if the question is unclear, respond with 'CANNOT_ANSWER'.

Answer:";

                var requestBody = new
                {
                    contents = new[]
                    {
                        new
                        {
                            parts = new[]
                            {
                                new { text = prompt }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_baseUrl}/models/{_model}:generateContent?key={_apiKey}";
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var geminiResponse = JsonSerializer.Deserialize<GeminiResponse>(responseContent);

                    var answerText = geminiResponse?.candidates?.FirstOrDefault()?.content?.parts?.FirstOrDefault()?.text?.Trim();

                    if (!string.IsNullOrEmpty(answerText) && answerText != "CANNOT_ANSWER" && answerText.Length > 50)
                    {
                        return new SuggestedAnswerDto
                        {
                            SourceQuestionId = null,
                            SourceQuestionTitle = "AI Generated Answer",
                            AnswerBody = answerText,
                            AnswerSource = "ai_generated",
                            RelevanceScore = 0.8, // AI answers get a default good score
                            OriginalAnswerId = null,
                            OriginalAuthor = null,
                            OriginalCreatedAt = null,
                            OriginalVoteCount = null,
                            IsAcceptedAnswer = false
                        };
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating AI answer: {ex.Message}");
                return null;
            }
        }

        public async Task<double> ScoreAnswerRelevanceAsync(string questionTitle, string questionBody, string answerBody)
        {
            try
            {
                var prompt = $@"
Rate how well this answer addresses the given question on a scale from 0.0 to 1.0.

Question Title: {questionTitle}
Question Body: {questionBody}

Answer: {answerBody}

Consider:
- Does the answer directly address the question?
- Is the answer technically correct and helpful?
- Would this answer solve the user's problem?

Respond with only a decimal number between 0.0 and 1.0 (e.g., 0.8), nothing else.";

                var requestBody = new
                {
                    contents = new[]
                    {
                        new
                        {
                            parts = new[]
                            {
                                new { text = prompt }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_baseUrl}/models/{_model}:generateContent?key={_apiKey}";
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var geminiResponse = JsonSerializer.Deserialize<GeminiResponse>(responseContent);

                    var scoreText = geminiResponse?.candidates?.FirstOrDefault()?.content?.parts?.FirstOrDefault()?.text?.Trim();

                    if (double.TryParse(scoreText, out double score))
                    {
                        return Math.Max(0.0, Math.Min(1.0, score)); // Ensure score is between 0 and 1
                    }
                }

                return 0.5; // Default moderate score if parsing fails
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error scoring answer relevance: {ex.Message}");
                return 0.5; // Default moderate score on error
            }
        }
    }

    // Helper classes for Gemini API response
    public class GeminiResponse
    {
        public GeminiCandidate[]? candidates { get; set; }
    }

    public class GeminiCandidate
    {
        public GeminiContent? content { get; set; }
    }

    public class GeminiContent
    {
        public GeminiPart[]? parts { get; set; }
    }

    public class GeminiPart
    {
        public string? text { get; set; }
    }
}
