{"version": 3, "file": "style-to-js.js", "sources": ["../node_modules/inline-style-parser/index.js", "../node_modules/style-to-object/cjs/index.js", "../cjs/utilities.js", "../cjs/index.js", "../cjs/index.js?commonjs-entry"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = StyleToObject;\nvar inline_style_parser_1 = __importDefault(require(\"inline-style-parser\"));\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nfunction StyleToObject(style, iterator) {\n    var styleObject = null;\n    if (!style || typeof style !== 'string') {\n        return styleObject;\n    }\n    var declarations = (0, inline_style_parser_1.default)(style);\n    var hasIterator = typeof iterator === 'function';\n    declarations.forEach(function (declaration) {\n        if (declaration.type !== 'declaration') {\n            return;\n        }\n        var property = declaration.property, value = declaration.value;\n        if (hasIterator) {\n            iterator(property, value, declaration);\n        }\n        else if (value) {\n            styleObject = styleObject || {};\n            styleObject[property] = value;\n        }\n    });\n    return styleObject;\n}\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.camelCase = void 0;\nvar CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nvar HYPHEN_REGEX = /-([a-z])/g;\nvar NO_HYPHEN_REGEX = /^[^-]+$/;\nvar VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nvar MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n/**\n * Checks whether to skip camelCase.\n */\nvar skipCamelCase = function (property) {\n    return !property ||\n        NO_HYPHEN_REGEX.test(property) ||\n        CUSTOM_PROPERTY_REGEX.test(property);\n};\n/**\n * Replacer that capitalizes first character.\n */\nvar capitalize = function (match, character) {\n    return character.toUpperCase();\n};\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nvar trimHyphen = function (match, prefix) { return \"\".concat(prefix, \"-\"); };\n/**\n * CamelCases a CSS property.\n */\nvar camelCase = function (property, options) {\n    if (options === void 0) { options = {}; }\n    if (skipCamelCase(property)) {\n        return property;\n    }\n    property = property.toLowerCase();\n    if (options.reactCompat) {\n        // `-ms` vendor prefix should not be capitalized\n        property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    else {\n        // for non-React, remove first hyphen so vendor prefix is not capitalized\n        property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    return property.replace(HYPHEN_REGEX, capitalize);\n};\nexports.camelCase = camelCase;\n//# sourceMappingURL=utilities.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar style_to_object_1 = __importDefault(require(\"style-to-object\"));\nvar utilities_1 = require(\"./utilities\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style, options) {\n    var output = {};\n    if (!style || typeof style !== 'string') {\n        return output;\n    }\n    (0, style_to_object_1.default)(style, function (property, value) {\n        // skip CSS comment\n        if (property && value) {\n            output[(0, utilities_1.camelCase)(property, options)] = value;\n        }\n    });\n    return output;\n}\nStyleToJS.default = StyleToJS;\nmodule.exports = StyleToJS;\n//# sourceMappingURL=index.js.map", "import { getDefaultExportFromCjs } from \"\u0000commonjsHelpers.js\";\nimport { __require as requireCjs } from \"/home/<USER>/work/style-to-js/style-to-js/cjs/index.js\";\nvar cjsExports = requireCjs();\nexport { cjsExports as __moduleExports };\nexport default /*@__PURE__*/getDefaultExportFromCjs(cjsExports);"], "names": ["this", "cjs", "require$$0", "require$$1"], "mappings": ";;;;;;;;;;;;;;;;;;CAAA;CACA;EACA,IAAI,aAAa,GAAG,iCAAiC,CAAC;AACtD;EACA,IAAI,aAAa,GAAG,KAAK,CAAC;EAC1B,IAAI,gBAAgB,GAAG,MAAM,CAAC;AAC9B;CACA;EACA,IAAI,cAAc,GAAG,wCAAwC,CAAC;EAC9D,IAAI,WAAW,GAAG,OAAO,CAAC;EAC1B,IAAI,WAAW,GAAG,sDAAsD,CAAC;EACzE,IAAI,eAAe,GAAG,SAAS,CAAC;AAChC;CACA;EACA,IAAI,UAAU,GAAG,YAAY,CAAC;AAC9B;CACA;EACA,IAAI,OAAO,GAAG,IAAI,CAAC;EACnB,IAAI,aAAa,GAAG,GAAG,CAAC;EACxB,IAAI,QAAQ,GAAG,GAAG,CAAC;EACnB,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB;CACA;EACA,IAAI,YAAY,GAAG,SAAS,CAAC;EAC7B,IAAI,gBAAgB,GAAG,aAAa,CAAC;AACrC;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAA,iBAAc,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE;CAC3C,GAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CACjC,KAAI,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;KACxD;AACH;CACA,GAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,GAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC1B;CACA;CACA;CACA;CACA,GAAE,IAAI,MAAM,GAAG,CAAC,CAAC;CACjB,GAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,cAAc,CAAC,GAAG,EAAE;MAC3B,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;MACrC,IAAI,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;MAClC,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;CACrC,KAAI,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;KACpD;AACH;CACA;CACA;CACA;CACA;CACA;IACE,SAAS,QAAQ,GAAG;CACtB,KAAI,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;MAC7C,OAAO,UAAU,IAAI,EAAE;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;QACpC,UAAU,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;CAClB,MAAK,CAAC;KACH;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;CAC3B,KAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACvB,KAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAChD,KAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;KAC9B;AACH;CACA;CACA;CACA;CACA,GAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;AAGrC;CACA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,KAAK,CAAC,GAAG,EAAE;CACtB,KAAI,IAAI,GAAG,GAAG,IAAI,KAAK;CACvB,OAAM,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG;CAC/D,MAAK,CAAC;CACN,KAAI,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC;CACrB,KAAI,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;CAClC,KAAI,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC;CACtB,KAAI,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;CACxB,KAAI,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;AACvB;CACA,KAAI,IAAI,OAAO,CAAC,MAAM,EAAE,CAEnB,MAAM;QACL,MAAM,GAAG,CAAC;OACX;KACF;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,KAAK,CAAC,EAAE,EAAE;MACjB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC3B,KAAI,IAAI,CAAC,CAAC,EAAE,OAAO;CACnB,KAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACnB,KAAI,cAAc,CAAC,GAAG,CAAC,CAAC;MACpB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;MAChC,OAAO,CAAC,CAAC;KACV;AACH;CACA;CACA;CACA;IACE,SAAS,UAAU,GAAG;CACxB,KAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;KACzB;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;MACvB,IAAI,CAAC,CAAC;CACV,KAAI,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;CACxB,KAAI,QAAQ,CAAC,GAAG,OAAO,EAAE,GAAG;CAC5B,OAAM,IAAI,CAAC,KAAK,KAAK,EAAE;CACvB,SAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACf;OACF;MACD,OAAO,KAAK,CAAC;KACd;AACH;CACA;CACA;CACA;CACA;CACA;CACA;IACE,SAAS,OAAO,GAAG;CACrB,KAAI,IAAI,GAAG,GAAG,QAAQ,EAAE,CAAC;CACzB,KAAI,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO;AAChF;CACA,KAAI,IAAI,CAAC,GAAG,CAAC,CAAC;MACV;CACJ,OAAM,YAAY,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;CACrC,QAAO,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACrE;QACA,EAAE,CAAC,CAAC;OACL;MACD,CAAC,IAAI,CAAC,CAAC;AACX;MACI,IAAI,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;CAC9C,OAAM,OAAO,KAAK,CAAC,wBAAwB,CAAC,CAAC;OACxC;AACL;CACA,KAAI,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;MAChC,MAAM,IAAI,CAAC,CAAC;CAChB,KAAI,cAAc,CAAC,GAAG,CAAC,CAAC;MACpB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;MACvB,MAAM,IAAI,CAAC,CAAC;AAChB;MACI,OAAO,GAAG,CAAC;QACT,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,GAAG;CAClB,MAAK,CAAC,CAAC;KACJ;AACH;CACA;CACA;CACA;CACA;CACA;CACA;IACE,SAAS,WAAW,GAAG;CACzB,KAAI,IAAI,GAAG,GAAG,QAAQ,EAAE,CAAC;AACzB;CACA;CACA,KAAI,IAAI,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;CACrC,KAAI,IAAI,CAAC,IAAI,EAAE,OAAO;MAClB,OAAO,EAAE,CAAC;AACd;CACA;CACA,KAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,KAAK,CAAC,sBAAsB,CAAC,CAAC;AAClE;CACA;CACA,KAAI,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;AACjC;CACA,KAAI,IAAI,GAAG,GAAG,GAAG,CAAC;QACZ,IAAI,EAAE,gBAAgB;CAC5B,OAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;QAC5D,KAAK,EAAE,GAAG;CAChB,WAAU,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;CAC3D,WAAU,YAAY;CACtB,MAAK,CAAC,CAAC;AACP;CACA;CACA,KAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AAC3B;MACI,OAAO,GAAG,CAAC;KACZ;AACH;CACA;CACA;CACA;CACA;CACA;IACE,SAAS,YAAY,GAAG;CAC1B,KAAI,IAAI,KAAK,GAAG,EAAE,CAAC;AACnB;CACA,KAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpB;CACA;MACI,IAAI,IAAI,CAAC;CACb,KAAI,QAAQ,IAAI,GAAG,WAAW,EAAE,GAAG;CACnC,OAAM,IAAI,IAAI,KAAK,KAAK,EAAE;CAC1B,SAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACzB,SAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjB;OACF;AACL;MACI,OAAO,KAAK,CAAC;KACd;AACH;IACE,UAAU,EAAE,CAAC;IACb,OAAO,YAAY,EAAE,CAAC;CACxB,EAAC,CAAC;AACF;CACA;CACA;CACA;CACA;CACA;CACA;EACA,SAAS,IAAI,CAAC,GAAG,EAAE;CACnB,GAAE,OAAO,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,GAAG,YAAY,CAAC;CACpE,EAAA;;;;;;;;;ECnQA,IAAI,eAAe,GAAG,CAACA,KAAI,IAAIA,KAAI,CAAC,eAAe,KAAK,UAAU,GAAG,EAAE;CACvE,KAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;CAC9D,EAAC,CAAC;CACF,CAAA,MAAM,CAAC,cAAc,CAACC,KAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;CAC9D,CAAeA,KAAA,CAAA,OAAA,GAAG,aAAa,CAAC;CAChC,CAAA,IAAI,qBAAqB,GAAG,eAAe,CAACC,wBAAA,EAA8B,CAAC,CAAC;CAC5E;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAA,SAAS,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE;CACxC,KAAI,IAAI,WAAW,GAAG,IAAI,CAAC;MACvB,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;UACrC,OAAO,WAAW,CAAC;OACtB;CACL,KAAI,IAAI,YAAY,GAAG,IAAI,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;CACjE,KAAI,IAAI,WAAW,GAAG,OAAO,QAAQ,KAAK,UAAU,CAAC;CACrD,KAAI,YAAY,CAAC,OAAO,CAAC,UAAU,WAAW,EAAE;CAChD,SAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,aAAa,EAAE;CAChD,aAAY,OAAO;WACV;CACT,SAAQ,IAAI,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;UAC/D,IAAI,WAAW,EAAE;cACb,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;WAC1C;eACI,IAAI,KAAK,EAAE;CACxB,aAAY,WAAW,GAAG,WAAW,IAAI,EAAE,CAAC;CAC5C,aAAY,WAAW,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;WACjC;CACT,MAAK,CAAC,CAAC;MACH,OAAO,WAAW,CAAC;GACtB;CACD,CAAA;;;;;;;;;;;CC1CA,CAAA,MAAM,CAAC,cAAc,CAAC,SAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;CAC9D,CAAiB,SAAA,CAAA,SAAA,GAAG,KAAK,CAAC,CAAC;EAC3B,IAAI,qBAAqB,GAAG,oBAAoB,CAAC;EACjD,IAAI,YAAY,GAAG,WAAW,CAAC;EAC/B,IAAI,eAAe,GAAG,SAAS,CAAC;EAChC,IAAI,mBAAmB,GAAG,4BAA4B,CAAC;EACvD,IAAI,sBAAsB,GAAG,SAAS,CAAC;CACvC;CACA;CACA;CACA,CAAA,IAAI,aAAa,GAAG,UAAU,QAAQ,EAAE;MACpC,OAAO,CAAC,QAAQ;CACpB,SAAQ,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;CACtC,SAAQ,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;CAC7C,EAAC,CAAC;CACF;CACA;CACA;CACA,CAAA,IAAI,UAAU,GAAG,UAAU,KAAK,EAAE,SAAS,EAAE;CAC7C,KAAI,OAAO,SAAS,CAAC,WAAW,EAAE,CAAC;CACnC,EAAC,CAAC;CACF;CACA;CACA;CACA,CAAA,IAAI,UAAU,GAAG,UAAU,KAAK,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;CAC7E;CACA;CACA;CACA,CAAA,IAAI,SAAS,GAAG,UAAU,QAAQ,EAAE,OAAO,EAAE;MACzC,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;CAC7C,KAAI,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;UACzB,OAAO,QAAQ,CAAC;OACnB;CACL,KAAI,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;CACtC,KAAI,IAAI,OAAO,CAAC,WAAW,EAAE;CAC7B;UACQ,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;OACnE;WACI;CACT;UACQ,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;OAChE;MACD,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;CACtD,EAAC,CAAC;CACF,CAAiB,SAAA,CAAA,SAAA,GAAG,SAAS,CAAC;CAC9B,CAAA;;;;;;;;;;EC7CA,IAAI,eAAe,GAAG,CAACF,GAAI,IAAIA,GAAI,CAAC,eAAe,KAAK,UAAU,GAAG,EAAE;CACvE,KAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;CAC9D,EAAC,CAAC;CACF,CAAA,IAAI,iBAAiB,GAAG,eAAe,CAACE,YAAA,EAA0B,CAAC,CAAC;EACpE,IAAI,WAAW,GAAGC,gBAAA,EAAsB,CAAC;CACzC;CACA;CACA;CACA,CAAA,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;CACnC,KAAI,IAAI,MAAM,GAAG,EAAE,CAAC;MAChB,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;UACrC,OAAO,MAAM,CAAC;OACjB;CACL,KAAI,IAAI,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,QAAQ,EAAE,KAAK,EAAE;CACrE;CACA,SAAQ,IAAI,QAAQ,IAAI,KAAK,EAAE;CAC/B,aAAY,MAAM,CAAC,IAAI,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC;WACjE;CACT,MAAK,CAAC,CAAC;MACH,OAAO,MAAM,CAAC;GACjB;CACD,CAAA,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC;CAC9B,CAAc,GAAA,GAAG,SAAS,CAAC;CAC3B,CAAA;;;;CCtBA,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;AAE9B,aAAe,aAAa,uBAAuB,CAAC,UAAU,CAAC;;;;;;;;", "x_google_ignoreList": [0, 1]}