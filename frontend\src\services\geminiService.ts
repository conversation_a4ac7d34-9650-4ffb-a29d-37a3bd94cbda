import { CreateQuestionRequest, SimilarQuestionsResponse } from "../types";

export const geminiService = {
  async checkSimilarity(
    questionData: CreateQuestionRequest
  ): Promise<SimilarQuestionsResponse> {
    try {
      const { apiClient } = await import("./api");
      const response = await apiClient.post<SimilarQuestionsResponse>(
        "/questions/check-similarity",
        questionData
      );
      return response;
    } catch (error) {
      console.error("Error checking question similarity:", error);
      // Return a safe fallback response
      return {
        hasSimilarQuestions: false,
        similarQuestions: [],
        suggestionMessage:
          "Unable to check for similar questions at this time. You can still post your question.",
      };
    }
  },
};
